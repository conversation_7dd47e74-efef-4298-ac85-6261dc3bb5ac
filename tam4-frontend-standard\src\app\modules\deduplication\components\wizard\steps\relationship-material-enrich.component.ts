import {Component, computed, effect, inject, Input, OnInit } from '@angular/core';
import {MaterialsEditorModule} from '../../../../materials-editor/materials-editor.module';
import {SmartCreationMaterialDetail, SmartFieldConfiguration} from '../../../../smart-creation/models/smart-creation.types';
import { ViewModeEnum} from '../../../../materials-editor/models/material-editor.types';
import {TamApePageType} from '../../../../../models';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import {FieldType, SelectorMap} from '@creactives/models';
import {TranslateService} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {InternalCommonModule} from '../../../../common/iternal-common.module';
import { DeduplicationFieldConfiguration} from '../../../models/deduplication.models';
import {DynamicFormInputFactory} from '../../../../materials-editor/service/dynamic-form-input.factory';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {MessageModule} from 'primeng/message';
import {DocumentData, DynamicComponentWrapper, SmartCreationFieldFetchSuggestionFn} from '../../../../materials-editor/dynamic-components';
import {ObjectsUtils} from '../../../../../utils';
import {DynamicFormInputService} from '../../../../materials-editor/service/dynamic-form-input.service';
import {Observable, switchMap, take} from 'rxjs';
import {
  AlternativeUnitsOfMeasure, SmartCreationDropdownDataRequest,
  SmartCreationReloadMaterialDetailsRequest
} from '../../../../smart-creation/models/smart-creation-validation.types';
import {SmartCreationFormControl} from '../../../../smart-creation/models/smart-creation-form.types';
import {SmartCreationService} from '../../../../smart-creation/smart-creation.service';
import {needFlatArray} from '../../../../smart-creation/commons/smart-creation.constants';

interface EnrichmentTableRow {
  fieldId: string;
  label: string;
  primary: DeduplicationFieldConfiguration;
  secondaries: DeduplicationFieldConfiguration[];
  groupTabLabel?: string;
  groupTabKey?: string;
}

interface EnrichmentTableColumn {
  field: string;
  header: string;
  isPrimary: boolean;
  materialKey?: string;
}

interface DynamicInputConfig {
  component: any;
  params: any;
  formControl: SmartCreationFormControl;
  editable: boolean;
}

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
];

@Component({
    selector: 'relationship-material-enrich',
    styleUrls: ['./relationship-material-enrich.component.scss'],
    template: `
      <div class="material-enrichment-container">
        <h3>{{ 'deduplication.enrichment.title' | translate }}</h3>

        @if (isLoading()) {
          <p-progressSpinner></p-progressSpinner>
          <p>{{ 'deduplication.enrichment.loading' | translate }}</p>
        } @else if (hasData() && tableRows()?.length > 0 && tableColumns()?.length > 0) {
          <p-table [value]="tableRows()"
                   styleClass="p-datatable-gridlines"
                   [scrollable]="true"
                   scrollHeight="500px"
                   [rowGroupMode]="'subheader'"
                   groupRowsBy="groupTabLabel"
          >
            <ng-template pTemplate="header">
              <tr>
                <th pFrozenColumn></th>
                <th pFrozenColumn>Primary</th>
                @for (column of tableColumns(); track column.field) {
                  @if (!column.isPrimary && column.field !== 'label') {
                    <th>
                      <p-checkbox
                          [binary]="true"
                          [ngModel]="isColumnAllSelected(column)"
                          (onChange)="onColumnSelectAll(column, $event.checked)"
                          [disabled]="!column.materialKey"
                          label="Select All">
                      </p-checkbox>
                    </th>
                  }
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="groupheader" let-rowData>
              <tr class="p-rowgroup-header">
                <td [attr.colspan]="tableColumns().length">
                  <span class="font-bold ml-2">{{ rowData.groupTabLabel }}</span>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-row>
              <tr>
                @for (column of tableColumns(); track column.field) {
                    @switch (column.field) {
                      @case ('label') {
                        <td pFrozenColumn>
                            <span >{{ row.label | attributeNameTranslate}}</span>
                        </td>
                      }
                      @case ('primary') {
                        <td pFrozenColumn>
                          @if (getPrimaryInputConfig(row.primary); as inputConfig) {
                            @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                              <span scDynamicContainerTemplate
                                    [dynamicComponent]="inputConfig.component"
                                    [dynamicParams]="inputConfig.params"
                                    [editable]="inputConfig.editable && editable"
                                    [viewMode]="type"
                                    [showEmptyValues]="showEmptyValues"
                                    [mandatory]="inputConfig.params?.mandatory"
                                    [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                    [disableCopy]="true"
                              >
                              </span>
                            }
                          } @else {
                            <span class="raw-value">{{ row.primary.value || '' }}</span>
                          }
                        </td>
                      }
                      @default {
                        <td>
                          @if (column.field.startsWith('secondary_')) {
                            @if (getSecondaryForColumn(row, column); as secondaryField) {
                                <p-checkbox
                                    [ngModel]="secondaryField.selected"
                                    (onChange)="onSecondaryFieldSelectionChange(row, secondaryField, $event.checked)"
                                    label="{{ (secondaryField.value || '') | attributeValueTranslate : secondaryField.id }}"
                                    [name]="secondaryField.id"
                                    [disabled]="!secondaryField.editable">
                                </p-checkbox>
                            } @else {
                              <span class="empty-cell"></span>
                            }
                          }
                        </td>
                      }
                    }
                }
              </tr>
            </ng-template>
            <ng-template pTemplate="footer">
              <tr>
                <td colspan="2" pFrozenColumn>
                  Hide Non editables
                  <p-checkbox
                    [binary]="true"
                    inputId="nonEditableCheck" />
                </td>
                <td colspan="{{ tableColumns().length - 2 }}"></td>
              </tr>
            </ng-template>
          </p-table>
        } @else {
          <p-message severity="info"
                     [text]="'deduplication.enrichment.noData' | translate">
          </p-message>
        }
      </div>
    `,
  imports: [
    MaterialsEditorModule,
    InternalCommonModule,
    ProgressSpinnerModule,
    MessageModule
  ],
  standalone: true
})
export class RelationshipMaterialEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentMaterialDetails = computed(() => this.signals?.enrichmentMaterialDetails());

  tableRows = computed(() => this.transformDataToTableRows());
  tableColumns = computed(() => this.generateTableColumns());

  isLoading = computed(() => !this.enrichmentMaterialDetails());
  hasData = computed(() => {
    const data = this.enrichmentMaterialDetails();
    return data && Array.isArray(data) && data.length > 0 &&
           data.some(group => group.rows && group.rows.length > 0);
  });

  disabledAttributes = [
      '4_TAM_UnitOfMeasure',
      '4_TAM_AlternativeUnitOfMeasure',
      '4_SDM_Client'
  ];

  type: ViewModeEnum = ViewModeEnum.EDIT;
  page: string = TamApePageType.EDIT;
  categoriesFormGroup: any;

  service = inject(DeduplicationService);
  dynamicFormInputFactory = inject(DynamicFormInputFactory);
  materialDetails: SmartCreationMaterialDetail;

  showEmptyValues = true;
  editable = true;

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);

    effect(() => {
      const enrichmentData = this.enrichmentMaterialDetails();
      if (enrichmentData) {
        console.log('Enrichment data updated:', enrichmentData);
        console.log('Table rows:', this.tableRows());
        console.log('Table columns:', this.tableColumns());
      }
    });
  }

  ngOnInit() {
    this.service.loadPrimaryMaterialDetails();
  }

  private transformDataToTableRows(): EnrichmentTableRow[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const rows: EnrichmentTableRow[] = [];

    // Process each group in the new structure
    data.forEach(group => {
      if (!group.rows || !Array.isArray(group.rows)) {
        return;
      }

      // Process each row within the group
      group.rows
          ?.filter((r: any) => !r.hidden)
          .filter((r: any) => !this.disabledAttributes.includes(r.id))
          .forEach((rowData: any) => {
        const fieldId = rowData.id;

        // Get primary field (should have one key)
        const primaryKeys = Object.keys(rowData.primary || {});
        const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;

        if (!primaryField) {
          return; // Skip if no primary field
        }

        // Get label from descriptionLanguages[0] if available, fallback to field label or id
        const label = primaryField.descriptionLanguages?.[0] || primaryField.label || fieldId;

        // Collect secondary fields for this fieldId
        const secondaries: DeduplicationFieldConfiguration[] = [];
        if (rowData.secondaries) {
          Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
            const secondaryField = rowData.secondaries[secondaryMaterialId];
            secondaries.push(secondaryField);
          });
        }
        rows.push({
          fieldId,
          label,
          primary: primaryField,
          secondaries,
          groupTabLabel: group.groupTabLabel,
          groupTabKey: group.groupTabKey
        });
      });
    });

    return rows;
  }

  private generateTableColumns(): EnrichmentTableColumn[] {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const columns: EnrichmentTableColumn[] = [
      // Label column
      { field: 'label', header: 'Field', isPrimary: false },
      // Primary column
      { field: 'primary', header: 'Primary', isPrimary: true }
    ];

    // Add secondary columns based on unique secondary material IDs from the new structure
    // Find the first group and first row to get secondary material IDs
    for (const group of data) {
      if (group.rows && group.rows.length > 0) {
        const firstRow = group.rows[0];
        if (firstRow.secondaries) {
          const secondaryMaterialIds = Object.keys(firstRow.secondaries);
          secondaryMaterialIds.forEach((materialId, index) => {
            columns.push({
              field: `secondary_${index}`,
              header: `${materialId} Secondary ${index + 1}`,
              isPrimary: false,
              materialKey: materialId
            });
          });
          break; // Only need to process the first row to get column structure
        }
      }
    }

    console.log(columns);
    return columns;
  }

  private createEnhancedParams(originalParams: any, additionalParams: { [key: string]: any } = {}): any {
    return {
      ...originalParams,
      ...additionalParams
    };
  }

  createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig | null {
    try {
      const formGroupInputs = this.initFormGroup(
          [fieldConfig],
          TamApePageType.EDIT,
          ViewModeEnum.EDIT,
          0,
          null,
          null,
          null,
          null);

      if (!formGroupInputs.items.length || !formGroupInputs.items[0].component || !formGroupInputs.items[0].componentParams) {
        console.warn('Dynamic input config non valido per', fieldConfig);
        return null;
      }

      // Get the form control using the correct key
      const formControlKey = fieldConfig.id;
      const formControl = formGroupInputs.formGroup.controls?.[formControlKey] as SmartCreationFormControl;

      if (!formControl) {
        console.warn('Form control not found for field:', formControlKey);
        return null;
      }

      // Usa il metodo helper per creare i parametri enhanced
      const enhancedParams = this.createEnhancedParams(
          formGroupInputs.items[0].componentParams,
          {
            // Qui puoi aggiungere altri parametri specifici se necessario
            editable: isEditable
          }
      );

      return {
        component: formGroupInputs.items[0].component,
        params: enhancedParams,
        formControl,
        editable: isEditable
      };
    } catch (error) {
      console.error('Error creating dynamic input config for field:', fieldConfig.id, error);
      return null;
    }
  }

  /**
   * Create dynamic input configuration for a field
   */
  // createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig | null {
  //   try {
  //     const formGroupInputs = this.initFormGroup(
  //         [fieldConfig],
  //         TamApePageType.EDIT,
  //         ViewModeEnum.EDIT,
  //         0,
  //         null,
  //         // this.fetchSuggestions.bind(this),
  //         null,
  //         null);
  //
  //     if (!formGroupInputs.items.length || !formGroupInputs.items[0].component || !formGroupInputs.items[0].componentParams) {
  //       console.warn('Dynamic input config non valido per', fieldConfig);
  //       return null;
  //     }
  //
  //     // Get the form control using the correct key
  //     const formControlKey = fieldConfig.id;
  //     const formControl = formGroupInputs.formGroup.controls?.[formControlKey] as SmartCreationFormControl;
  //
  //     if (!formControl) {
  //       console.warn('Form control not found for field:', formControlKey);
  //       return null;
  //     }
  //
  //     return {
  //       component: formGroupInputs.items[0].component,
  //       params: formGroupInputs.items[0].componentParams,
  //       formControl,
  //       editable: isEditable
  //     };
  //   } catch (error) {
  //     console.error('Error creating dynamic input config for field:', fieldConfig.id, error);
  //     return null;
  //   }
  // }


  // fetchSuggestions(source: string,
  //                  id: string,
  //                  query: string,
  //                  documentData: DocumentData,
  //                  clientId?: string,
  //                  page?: string,
  // ): Observable<any> {
  //   const fetchSuggestionContext = this.getFetchSuggestionContext(page);
  //   return fetchSuggestionContext.pipe(
  //       take(1),
  //       switchMap(([
  //                    currentLanguage,
  //                    fallBackLanguages,
  //                    selectedClients,
  //                    selectedCategories,
  //                    selectedDomain,
  //                    materialDetail,
  //                    isGoldenRecordEnabled,
  //                    innerPage,
  //                    completeness,
  //                    processId,
  //                    materialId
  //                  ]) => {
  //
  //         const client = clientId || selectedClients;
  //
  //         const requestBody: SmartCreationReloadMaterialDetailsRequest =
  //             this.getBaseSmartMaterialDetailsRequest(
  //             currentLanguage,
  //             fallBackLanguages,
  //             client,
  //             selectedCategories,
  //             selectedDomain,
  //             materialDetail,
  //             isGoldenRecordEnabled,
  //             innerPage,
  //             completeness,
  //             processId,
  //             materialId
  //         );
  //         // if (processId===undefined||  (TamApePageUtils.isSmartCreationPage(page))
  //         // fixme: need a more elegant way to do this!
  //         this.normalizeDataFromDocumentPlantForm(documentData, requestBody, client);
  //
  //         const body: SmartCreationDropdownDataRequest = {
  //           ...requestBody,
  //           client,
  //           queryText: query,
  //           language: currentLanguage
  //         };
  //         return this.dao.getDropdownData(source, id, body);
  //       })
  //   );
  // }

  private initFormGroup(
      sheets: SmartFieldConfiguration[],
      page: string,
      viewMode: ViewModeEnum,
      sheetIndex: number,
      formGroup?: FormGroup,
      dynamicAutocompleteFn?: (source: string, id: string, documentData: DocumentData) => Observable<any>,
      initialData?: SmartCreationMaterialDetail,
      updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void
  ) {
    const items = [];

    if (!formGroup) {
      formGroup = new FormGroup({});
    }

    sheets?.forEach((smartFieldConfiguration: SmartFieldConfiguration) => {
      formGroup.enable({ onlySelf: true, emitEvent: false });

      // Aggiunta controllo se non esiste già
      if (ObjectsUtils.isNoU(formGroup.controls?.[smartFieldConfiguration.id])) {
        const control: SmartCreationFormControl = this.initFormControl(smartFieldConfiguration);
        formGroup.addControl(smartFieldConfiguration.id, control);
      }

      // Pulizia dei controlli non più presenti nella configurazione
      Object.keys(formGroup.controls).forEach(key => {
        const baseKey = key.replace(".mu", "");
        const existsInSheet = sheets.some(k => k.id === baseKey);
        if (!existsInSheet) {
          formGroup.removeControl(key);
        }
      });

      // Abilita translate pipe se serve
      smartFieldConfiguration.useTranslatePipe = this.setUseTranslatePipe(smartFieldConfiguration.id);

      // Costruzione componente dinamico
      const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(
          smartFieldConfiguration,
          page,
          formGroup,
          sheetIndex,
          smartFieldConfiguration.order,
          dynamicAutocompleteFn,
          initialData,
          updateAlternativeUomFn,
          viewMode
      );

      // console.log("initFormGroup", dynamicComponentWrapper);

      items.push({
        ...dynamicComponentWrapper,
        ...smartFieldConfiguration
      });
    });

    return {
      formGroup,
      items
    };
  }


  private componentBasedOnType(v: SmartFieldConfiguration,
                               page: string,
                               formGroup: FormGroup,
                               sheetIndex: number,
                               tabIndex: number,
                               dynamicAutocompleteFn?,
                               initialData?: SmartCreationMaterialDetail,
                               updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void,
                               viewMode?: ViewModeEnum): DynamicComponentWrapper {

      return this.dynamicFormInputFactory.buildDynamicFormInput(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
    }

  private setUseTranslatePipe(fieldName: string): string {

      switch (fieldName) {
        case '4_SDM_Plant':
          return 'plant';
        case '4_SDM_Client':
          return 'client';
        default:
          return null;
      }
    }

  private initFormControl(ctrl: SmartFieldConfiguration): SmartCreationFormControl {
    const value = this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl?.value) : ctrl?.value;
    return new SmartCreationFormControl(value,
        ctrl.mandatory ? [Validators.required] : [],
        null,
        ctrl.label,
        ctrl.dropdownValues && ctrl.dropdownValues.length > 0 ? ctrl.dropdownValues : null,
        'smartCreation.smartValidation.error'
    );
  }

  private needArrayConversion(cfg: any) {
    return needFlatArray(cfg);
  }
  /**
   * Get dynamic input configuration for primary field (editable)
   */
  getPrimaryInputConfig(fieldConfig: any): DynamicInputConfig | null {
    return this.createDynamicInputConfig(fieldConfig, fieldConfig.editable);
  }

  /**
   * Get secondary field for a specific column
   */
  getSecondaryForColumn(row: EnrichmentTableRow, column: EnrichmentTableColumn): DeduplicationFieldConfiguration | null {
    if (!column.field.startsWith('secondary_')) {
      return null;
    }

    const secondaryIndex = parseInt(column.field.replace('secondary_', ''), 10);
    return row.secondaries[secondaryIndex] || null;
  }

  /**
   * Handle individual checkbox selection
   */
  onSecondaryFieldSelectionChange(
    row: EnrichmentTableRow,
    secondaryField: DeduplicationFieldConfiguration,
    selected: boolean
  ) {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return;
    }

    // Find the group and row indices
    let groupIndex = -1;
    let rowIndex = -1;

    for (let gIdx = 0; gIdx < data.length; gIdx++) {
      const group = data[gIdx];
      if (group.rows && Array.isArray(group.rows)) {
        for (let rIdx = 0; rIdx < group.rows.length; rIdx++) {
          const currentRow = group.rows[rIdx];
          if (currentRow.id === row.fieldId) {
            groupIndex = gIdx;
            rowIndex = rIdx;
            break;
          }
        }
      }
      if (groupIndex !== -1) { break; }
    }

    if (groupIndex !== -1 && rowIndex !== -1) {
      // Find the secondary material ID
      const secondaryMaterialId = this.findSecondaryMaterialId(row, secondaryField);

      if (secondaryMaterialId) {
        this.service.updateEnrichmentFieldSelection({
          groupIndex,
          rowIndex,
          fieldId: row.fieldId,
          secondaryMaterialId,
          selected,
          value: secondaryField.value
        });
      }
    }
  }

  /**
   * Handle column-wide selection
   */
  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    if (column.materialKey) {
      this.service.bulkSelectEnrichmentColumn({
        columnMaterialId: column.materialKey,
        selected
      });
    }
  }

  /**
   * Check if all items in a column are selected
   */
  isColumnAllSelected(column: EnrichmentTableColumn): boolean {
    const rows = this.tableRows();
    if (!rows || !column.materialKey) {
      return false;
    }

    const editableRows = rows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.editable;
    });

    if (editableRows.length === 0) {
      return false;
    }

    return editableRows.every(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.selected;
    });
  }

  /**
   * Check if some items in a column are selected (for indeterminate state)
   */
  isColumnSomeSelected(column: EnrichmentTableColumn): boolean {
    const rows = this.tableRows();
    if (!rows || !column.materialKey) {
      return false;
    }

    const editableRows = rows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.editable;
    });

    if (editableRows.length === 0) {
      return false;
    }

    const selectedCount = editableRows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.selected;
    }).length;

    return selectedCount > 0 && selectedCount < editableRows.length;
  }

  /**
   * Find the secondary material ID for a given secondary field
   */
  private findSecondaryMaterialId(row: EnrichmentTableRow, targetField: DeduplicationFieldConfiguration): string | null {
    const data = this.enrichmentMaterialDetails();
    if (!data || !Array.isArray(data)) {
      return null;
    }

    // Find the row in the data structure
    for (const group of data) {
      if (group.rows && Array.isArray(group.rows)) {
        for (const dataRow of group.rows) {
          if (dataRow.id === row.fieldId && dataRow.secondaries) {
            // Find the material ID that corresponds to this secondary field
            for (const [materialId, secondaryField] of Object.entries(dataRow.secondaries)) {
              // Cast to DeduplicationFieldConfiguration to access properties
              const typedSecondaryField = secondaryField as DeduplicationFieldConfiguration;
              if (typedSecondaryField.id === targetField.id) {
                return materialId;
              }
            }
          }
        }
      }
    }

    return null;
  }

}
